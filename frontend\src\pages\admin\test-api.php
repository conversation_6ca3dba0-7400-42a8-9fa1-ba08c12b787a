<?php
// Test API endpoints
echo "<h2>🧪 Test API Endpoints</h2>";

// Test get_user_stats
echo "<h3>1. Test get_user_stats</h3>";
echo "<p><strong>URL:</strong> <a href='api.php?action=get_user_stats' target='_blank'>api.php?action=get_user_stats</a></p>";

// Make internal request
$_GET['action'] = 'get_user_stats';
ob_start();
include 'api.php';
$response = ob_get_clean();

echo "<p><strong>Response:</strong></p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($response);
echo "</pre>";

// Test add_user endpoint
echo "<h3>2. Test add_user (POST)</h3>";
echo "<form method='POST' action='api.php' style='background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<input type='hidden' name='action' value='add_user'>";
echo "<p><label>Name: <input type='text' name='name' value='Test User' required></label></p>";
echo "<p><label>Email: <input type='email' name='email' value='<EMAIL>' required></label></p>";
echo "<p><label>Password: <input type='password' name='password' value='123456' required></label></p>";
echo "<p><label>Role: <select name='role'><option value='user'>User</option><option value='admin'>Admin</option></select></label></p>";
echo "<p><label>Status: <select name='status'><option value='active'>Active</option></select></label></p>";
echo "<p><label><input type='checkbox' name='generate_jwt' value='1' checked> Generate JWT</label></p>";
echo "<p><button type='submit'>Test Add User</button></p>";
echo "</form>";

// Show current directory and file structure
echo "<h3>3. File Structure Debug</h3>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>API File Exists:</strong> " . (file_exists(__DIR__ . '/api.php') ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Config File Exists:</strong> " . (file_exists(__DIR__ . '/config.php') ? '✅ Yes' : '❌ No') . "</p>";

// Test database connection
echo "<h3>4. Database Connection Test</h3>";
try {
    require_once 'config.php';
    $pdo = getConnection();
    echo "<p>✅ Database connection successful</p>";
    
    // Test users table
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>📊 Total users in database: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// JavaScript test
echo "<h3>5. JavaScript API Test</h3>";
echo "<button onclick='testAPI()'>Test API with JavaScript</button>";
echo "<div id='jsResult' style='margin-top: 10px; padding: 10px; background: #f0f0f0; border-radius: 5px;'></div>";

echo "<script>
function testAPI() {
    const resultDiv = document.getElementById('jsResult');
    resultDiv.innerHTML = '🔄 Testing API...';
    
    fetch('api.php?action=get_user_stats')
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response URL:', response.url);
            return response.json();
        })
        .then(data => {
            resultDiv.innerHTML = '<strong>✅ Success!</strong><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            resultDiv.innerHTML = '<strong>❌ Error:</strong> ' + error.message;
            console.error('API Error:', error);
        });
}
</script>";

echo "<p><a href='dashboard.php'>← Back to Dashboard</a></p>";
?>
